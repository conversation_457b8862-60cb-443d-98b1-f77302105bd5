<template>
  <div class="window-nav">
    <!--    <div class="nav-logo">-->
    <!--      <img-->
    <!--        src="../../assets/desktop/nav/logo-desktop-nav.png"-->
    <!--        alt=""-->
    <!--      >-->
    <!--    </div>-->
    <div class="nav-title">
      <img
        src="../../assets/desktop/nav/windows-nav-title.png"
        alt=""
      >
    </div>
    <!--    <div class="nav-search">-->
    <!--      <header-search/>-->
    <!--    </div>-->
    <div class="nav-list">
      <div
        v-for="(item, index) in navList"
        :key="index"
        :style="{
          left: item.left,
          right: item.right,
          top: item.top
        }"
        class="nav-item"
        @click="openPage(item)">
        <img
          :src="item.imgSrc"
          alt=""
        >
      </div>
    </div>
    <el-dialog
      :top="'0%'"
      :visible.sync="navVisible"
      :close-on-click-modal="false"
      :destroy-on-close="false"
      :width="'100%'"
      :show-close="false"
      :append-to-body="true"
      lock-scroll
      class="hmi-dialog"
      @close="navVisible = false">
      <div
        slot="title"
        class="header"/>
      <div class="nav-wrapper">
        <div class="nav-operate">
          <el-tooltip
            class="item"
            effect="dark"
            content="关闭"
            placement="bottom">
            <i
              class="el-icon-close"
              @click="navVisible = false"
            />
          </el-tooltip>
        </div>
        <div class="nav-title">
          <img
            src="../../assets/images/screen/screen-nav-title.png"
            alt=""
          >
        </div>
        <div class="nav-list-wrapper">
          <el-row :gutter="60">
            <el-col :span="12">
              <div class="nav-list">
                <img
                  src="../../assets/主题看板.png"
                  alt=""
                >
                <div class="list-inner">
                  <div
                    v-for="(item, index) in themeList"
                    :key="index"
                    class="nav-item"
                    @click="openScreen(item.data)">
                    {{ item.disName }}
                    <div
                      v-if="item.children && item.children.length"
                      class="nav-sub">
                      <div
                        v-for="sub in item.children"
                        :key="sub.url"
                        class="sub-item">
                        {{ sub.disName }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="nav-list">
                <img
                  src="../../assets/晨会看板.png"
                  alt=""
                >
                <div class="list-inner">
                  <div
                    v-for="(item, index) in meetingList"
                    :key="index"
                    class="nav-item"
                    @click="openScreen(item.data)">
                    {{ item.disName }}
                    <div
                      v-if="item.children && item.children.length"
                      class="nav-sub">
                      <div class="nav-sub-inner">
                        <div
                          v-for="sub in item.children"
                          :key="sub.url"
                          class="sub-item"
                          @click.stop="openScreen(sub.data)">
                          {{ sub.disName }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { findBasicDataConfigByType } from '@/api/system'
import { post } from '@/lib/Util'
import ScreenNav from '@/components/index/screenNav'
import HeaderSearch from '@/components/index/HeaderSearch'

export default {
  name: 'WindowsNav',
  components: { HeaderSearch, ScreenNav },
  data: () => {
    return {
      navVisible: false,
      serviceList: [],
      themeList: [
        {
          name: '能源主题看板',
          disName: '能源主题看板',
          serviceName: 'kpi'
        },
        {
          name: '运维主题看板',
          disName: '运维主题看板',
          serviceName: 'kpi'
        },
        {
          name: '生产关键指标看板',
          disName: '生产主题看板',
          serviceName: 'kpi'
        },
        {
          name: '技术研发处看板',
          disName: '技术研发处看板',
          serviceName: 'kpi'
        },
        {
          name: '治理设施参数看板',
          disName: '治理设施参数看板',
          serviceName: 'kpi'
        },
        {
          name: '热处理攻关看板',
          disName: '热处理攻关看板',
          serviceName: 'kpi'
        },
        {
          name: '全流程收得率',
          disName: '全流程收得率',
          serviceName: 'kpi'
        }
      ],
      meetingList: [
        {
          name: '生产晨会看板',
          disName: '生产晨会看板',
          serviceName: 'kpi'
        },
        {
          name: '质量晨会看板',
          disName: '质量晨会看板',
          serviceName: 'kpi'
        }
      ],
      navList: [
        {
          name: '智慧生产',
          imgSrc: require('../../assets/desktop/nav/zhsc.png'),
          left: '15%',
          right: 0,
          top: '28%',
          serviceName: 'ipd'
        },
        {
          name: '智慧质量',
          imgSrc: require('../../assets/desktop/nav/zhzl.png'),
          left: 'auto',
          right: '15%',
          top: '28%',
          serviceName: 'qms'
        },
        {
          name: '智慧能源',
          imgSrc: require('../../assets/desktop/nav/zhny.png'),
          left: '5%',
          right: 0,
          top: '45%',
          serviceName: 'ems'
        },
        {
          name: '智慧成本',
          imgSrc: require('../../assets/desktop/nav/zhcb.png'),
          left: 'auto',
          right: '5%',
          top: '45%',
          serviceName: 'ifc'
        },
        {
          name: '智慧运维',
          imgSrc: require('../../assets/desktop/nav/zhyw.png'),
          left: '10%',
          right: '',
          top: '62%',
          serviceName: 'iom'
        },
        {
          name: '协同运管',
          imgSrc: require('../../assets/desktop/nav/xtyg.png'),
          left: '',
          right: '10%',
          top: '62%',
          serviceName: 'kpi'
        },
        {
          name: '智慧看板',
          imgSrc: require('../../assets/desktop/nav/zhkb.png'),
          left: '27%',
          right: '',
          top: '79%',
          serviceName: 'kpi'
        },
        {
          name: '页面访问统计看板',
          imgSrc: require('../../assets/desktop/nav/xtgl.png'),
          left: '',
          right: '27%',
          top: '79%',
          serviceName: 'res'
        }
      ]
    }
  },
  computed: {
    ...mapState('menu', ['allMenus'])
  },
  watch: {
    allMenus: function(newValue) {
      this.formatList(newValue)
    }
  },
  created() {
    this.findBasicDataConfigByType()
    this.formatList(this.allMenus)
  },
  methods: {
    formatList(list) {
      const themeList = []
      let meetingList = []
      list.forEach(item => {
        if (item.code.indexOf('screen-theme') !== -1) {
          themeList.push({
            name: item.name,
            disName: item.name,
            data: item
          })
        } else if (item.code.indexOf('screen-meeting') !== -1) {
          meetingList.push({
            name: item.name,
            disName: item.name,
            data: item
          })
        }
      })
      // console.log(meetingList, themeList)
      this.themeList = themeList.sort((a, b) => a.data.sort - b.data.sort)
      // 合并看板列表
      const factoryList = ['第一炼钢厂', '中厚板卷厂']
      factoryList.forEach(factory => {
        const screens = meetingList
          .filter(item => item.name.indexOf(factory) !== -1)
          .map(item => {
            item.disName = item.name.replace(factory, '').replace('看板', '')
            return item
          })
        if (!screens.length) return
        meetingList.push({
          name: '',
          disName: `${factory}看板`,
          data: {
            sort: meetingList.find(item => item.name.indexOf(factory) !== -1)
              .data.sort
          },
          children: screens
        })
        meetingList = meetingList.filter(
          item => item.name.indexOf(factory) === -1
        )
      })
      this.meetingList = meetingList.sort((a, b) => a.data.sort - b.data.sort)

      // 如果包含"编辑"或"查看"，则直接过滤掉
      this.meetingList = this.meetingList.filter(item => {
        return !item.disName.includes('编辑') && !item.disName.includes('查看')
      })
    },
    openPage(nav) {
      // console.log(nav)
      if (nav.name === '智慧看板') return (this.navVisible = true)
      if (!nav.serviceName) {
        return this.$message.warning('未获取到菜单配置，请联系管理员')
      }
      const matchMenu = this.allMenus.find(
        item =>
          item.name === nav.name &&
          item.serviceName === nav.serviceName &&
          item.type === 'menu'
      )
      if (!matchMenu) {
        return this.$message.warning('您暂无权限查看此模块')
      }
      console.log(matchMenu)
      this.$bus.$emit('open-iframe', matchMenu)
    },
    openScreen(menu) {
      if (menu.url) {
        this.$bus.$emit('open-iframe', menu)
      }
    },
    async findBasicDataConfigByType(type) {
      // var data = JSON.parse(info)
      const data = await post(
        findBasicDataConfigByType,
        {
          type: 'serviceInfo'
        },
        'no'
      )
      let dataContent = []
      if (data.content) {
        dataContent = JSON.parse(data.content)
        data.content = dataContent
        if (data.flag === 'json') {
          var dataTypeContent = []
          dataContent.map(function(item, index) {
            var itemType = typeof item === 'string' ? JSON.parse(item) : item
            dataTypeContent.push(itemType)
          })
          data.content = dataTypeContent
        }
      }
      this.serviceList = data.content
      this.navList.forEach(item => {
        const matchService = this.serviceList.find(
          service => service.cname === item.name
        )
        if (matchService) {
          item.serviceName = matchService.name
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.window-nav {
  position: relative;
  height: 100%;
  width: 100%;
  background: #0d1837 url(../../assets/desktop/desktop-bg/window-nav-bg.png)
    no-repeat center;
  background-size: cover;
  .nav-logo {
    position: absolute;
    top: 4%;
    left: 2%;
    height: 72px;
    img {
      height: 100%;
      width: auto;
    }
  }
  img {
    width: 100%;
    user-select: none;
    -webkit-user-drag: none;
  }
  .nav-title {
    position: absolute;
    left: 0;
    right: 0;
    top: 8%;
    width: 45%;
    margin: auto;
  }
  .nav-search {
    position: absolute;
    left: 0;
    right: 0;
    top: 19%;
    width: 18%;
    margin: auto;
  }
  .nav-list {
    color: #fff;
    .nav-item {
      position: absolute;
      width: 19%;
      cursor: pointer;
      &:hover {
        filter: brightness(200%);
        transform: translate(0px, -1px);
      }
    }
  }
}

.nav-wrapper {
  height: 100%;
  background: url(../../assets/images/screen/screen-nav-bg.png) top no-repeat;
  background-size: cover;
  position: relative;
  .nav-list-wrapper {
    position: absolute;
    top: 18%;
    left: 50%;
    width: 1280px;
    transform: translateX(-50%);
    color: #fff;
  }
  .nav-list {
    position: relative;
    img {
      width: 100%;
    }
  }
  .nav-title {
    position: absolute;
    top: 2.5%;
    left: 50%;
    transform: translateX(-50%);
  }
  .list-inner {
    position: absolute;
    width: 60%;
    top: 20%;
    left: 0;
    right: 0;
    margin: auto;
    .nav-item {
      position: relative;
      height: 52px;
      font-size: 30px;
      line-height: 52px;
      text-align: center;
      cursor: pointer;
      margin-bottom: 16px;
      border-top: 1px solid transparent;
      border-bottom: 1px solid transparent;
      border-image: linear-gradient(90deg, #1f2f60, #4370fa 50%, #1f2f60) 30 30;
      &:after {
        content: '';
        position: absolute;
        left: -10px;
        width: 8px;
        height: 100%;
        background: url(../../assets/images/screen/screen-border.png) no-repeat
          center;
        transform: rotate(180deg);
      }
      &:before {
        content: '';
        position: absolute;
        right: -10px;
        width: 8px;
        height: 100%;
        background: url(../../assets/images/screen/screen-border.png) no-repeat
          center;
      }
      &:hover {
        color: rgba(171, 203, 241);
        .nav-sub {
          display: block;
        }
      }
      .nav-sub {
        display: none;
        position: absolute;
        left: 100%;
        top: 0;
        padding-left: 10px;
        .nav-sub-inner {
          width: 140px;
          background-color: #182757;
          color: #fff;
          box-shadow: 1px 1px 2px 2px #213577;
          border-radius: 6px;
        }
        .sub-item {
          height: 40px;
          font-size: 20px;
          line-height: 40px;
          &:hover {
            background-color: #213162;
            color: rgba(171, 203, 241);
          }
        }
      }
    }
  }
  .nav-operate {
    position: absolute;
    top: 10px;
    right: 10px;
    i {
      color: #fff;
      font-size: 22px;
      cursor: pointer;
    }
  }
}

.hmi-dialog {
  /deep/ .el-dialog {
    margin: 0;
    overflow: hidden;
    background: transparent;
  }
  /deep/ .el-dialog__header {
    padding: 0;
  }
  /deep/ .el-dialog__body {
    height: calc(100vh);
    background-color: #cbd0e3;
    padding: 0px;
  }
  .header {
    padding: 0 12px;
    height: 0;
    background-color: #13356f;
    color: #fff;
    font-size: 18px;
    letter-spacing: 1px;
    line-height: 30px;
    display: flex;
    justify-content: space-between;
    > div {
    }
    .oper {
      > i {
        margin-right: 4px;
        cursor: pointer;
      }
    }
  }
}
</style>
