// 路由守卫
import {
  filterUrl,
  funcUrlDel,
  getMenus,
  getTokenStatus,
  goToLogin,
  initialOpenedList,
  openNewPage
} from '@/lib/Menu'

// 无需登陆白名单
const whiteList = ['/login', '/password', '/noLogin/WidgetWarningSituation']
const sleep = function timeout(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}
export default ({ app }) => {
  app.router.afterEach((to, from) => {
    openNewPage(app, to.name, to.path, to.fullPath, to.params, to.query)
    window.scrollTo(0, 0)
  })
  app.router.beforeEach(async (to, from, next) => {
    // console.log(to)
    const { query } = to
    // 判断资源来源是否为后端重定向
    // 获取url参数，如果有token信息，直接塞入localstorage
    if (query.org && query.org === 'redirect') {
      !!query.token && localStorage.setItem('token', query.token)
      if (!!query.userId) {
        // 清空已打开窗口
        localStorage.getItem('userId') !== query.userId &&
          app.store.commit('desktop/miniAppList', [])
        localStorage.setItem('userId', query.userId)
      }
    }
    // 去除路由参数 userId & token
    delete query.token
    delete query.userId
    // console.log(to)
    if (whiteList.includes(to.path)) {
      return next()
    }
    // 判断系统token
    if (!getTokenStatus()) {
      // 没有token或token失效,记录当前页面Url
      // console.warn('没有token，开始重定向')
      // 直接跳转到统一认证登录页
      // 登录
      const _login = await goToLogin(app)
      _login && next('/login')
    } else {
      next()
      // 重定向时，删除页面token参数
      if (query.org && query.org === 'redirect') {
        funcUrlDel()
      }
      // 用户菜单没有加载时直接请求用户权限菜单
      if (
        // windows 对象上的系统初始化标志位
        !window.SYSTEM_MENU_LOADED
      ) {
        // 系统菜单已加载动作已执行
        window.SYSTEM_MENU_LOADED = 1
        // 系统初始化时，设置头部显示
        app.store.commit('menu/showHeader', true)
        // 判断是否显示头部
        query.showHeader !== undefined &&
          app.store.commit('menu/showHeader', !!parseInt(query.showHeader))
        // console.log('重新请求数据')
        await getMenus(app.store)
        // 初始化标签固定页
        initialOpenedList(app)
      }

      // 判断用户访问的路由权限是否存在
      if (
        app.store.state.menu.allMenus &&
        !app.store.state.menu.allMenus.find(
          item =>
            item.url === to.path ||
            item.url === decodeURIComponent(filterUrl(to.fullPath))
        ) &&
        !['/'].includes(to.path)
      ) {
        next('/')
      } else {
        next()
      }
    }
  })
}
